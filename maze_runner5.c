#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <string.h>
#include <time.h>
#include <limits.h>
#include "types.h"

                // north, east, south, west
const int dirRows[4] = {-1, 0, 1, 0};
const int dirCol[4]   = {0, 1, 0, -1};


Cell BOARD[FLOORS][ROWS][COLUMNS];
BawanaType BAWANA_MAP[BAWANA_ROW_END-BAWANA_ROW_START+1][BAWANA_COL_END-BAWANA_COL_START+1];
Player PLAYERS[NUM_PLAYERS];

int n_stairs = 0;
int n_poles = 0;
int n_walls = 0;
Stair STAIRS[CELLS];
Pole POLES[CELLS];
Wall WALLS[CELLS];


Position FLAG;

int game_round = 0;

// debug
int debug_bawana_visits = 0;
int debug_stairs_taken = 0;
int debug_poles_taken = 0;
int debug_wall_blocks = 0;

// functions
void load_seed();
void apply_cell_effect(Player *p, const Cell *cell);
void setup_players();
bool same_pos(Position a, Position b);
void place_flag_randomly();
void blocked_cost(Player *P);
bool attempt_move(Player *P, int steps, bool *sent_to_bawana, bool *capture, int *cells_moved, long long *cost);
void check_capture(Player* P, bool *captured);
Cell *check_pole(Cell *current);
Cell *check_stairs(Cell *current);
bool can_use_stair(Stair *stair, Position current_pos);
int man_best_distance(Position pos1, Position pos2);
void initialize_poles();
void initialize_stairs();
void stairs_bidirectional();
void randomize_stairs_direction();
void apply_bawana(Player *p);
void exit_bawana(Player *p);
BawanaType random_bawana_cell ();
void initialize_bawana();
void consumables_randomize();
void add_wall(int floor, int col1, int col2, int row1, int row2);
int min(int a, int b);
int max(int a, int b);
void init_cells();
void init_walls();
void set_cell (Cell *cell, CellKind kind, int consumables, int add, int mul);
bool is_in_board (int row, int col);
bool is_cell_available(int floor, int row, int col);
bool is_cell_available_for_poles(int floor, int row, int col);
Cell* get_cell (int floor, int row, int col);
int set_direction(int die);
int rand_int(int low, int high);
bool is_valid_direction(int d);
char* direction_name(Direction dir);
void print_stair_dir();
void print_debug_out();
void load_stairs();
void load_poles();
void load_walls();
void load_flag();
int get_cell_number(Position pos);
void block_stair_passing_cells(Stair *stair);

// main
int main() {
    
    // files
    load_seed();
    load_stairs();
    load_poles();
    load_walls();

    // initialize
    init_cells();
    init_walls();
    consumables_randomize();
    initialize_bawana();
    initialize_stairs();
    initialize_poles();
    load_flag();


    Player A,B,C;
    PLAYERS[0] =A;
    PLAYERS[1] =B;
    PLAYERS[2] =C;
    setup_players();

    printf("========================================\n");
    printf("||           MAZE RUNNER               ||\n");
    printf("========================================\n\n");
    printf(" Flag placed at [Floor:%d, Row:%d, Col:%d]\n\n", FLAG.floor, FLAG.row, FLAG.col);

    bool game_over = false;


    for (game_round=0; game_round<GAME_ROUNDS && !game_over; game_round++){
        printf("\n=========================== ROUND %d ==============================\n", game_round+1);
        if (game_round%STAIRS_CHANGE_EVERY_ROUNDS==0 && game_round!=0) {
            randomize_stairs_direction();
             printf(" Stairs direction changed\n");
             print_stair_dir();
        }

        for (int player_round = 0; player_round<NUM_PLAYERS; player_round++){
            Player *P = &PLAYERS[player_round];
            char *message= "";
            printf("\n--------------------------------------------------------------------\n");
            printf("│ Round %3d │ Player %c │ MP: %5lld │ Pos: [%d,%d,%d] │ Dir: %s │\n",
                   game_round+1, P->name, P->mp, P->position.floor, P->position.row, P->position.col, direction_name(P->direction));
            printf("--------------------------------------------------------------------\n");

            if (P->turns_skipped > 0){
                P->turns_skipped--;
                printf("%c is still food poisoned and misses the turn.\n", P->name);
                if (P->turns_skipped==0) {
                    sprintf(message,"%c is now fit to proceed from the food poisoning episode and now placed on a ", P->name);
                    apply_bawana(P);
                }
                continue;
            }
            int move_die = rand_int(1,6);
            if (!P->in_maze) {
                if (move_die==6) {
                    printf("%c is at the starting area and rolls 6 on the movement dice and is placed on %d of the maze.\n",
                           P->name, P->start_cell.floor * 100 + P->start_cell.row * 10 + P->start_cell.col);
                    P->position = P->start_cell;
                    P->in_maze = true;
                    P->direction=P->start_direction;
                    apply_cell_effect(P,get_cell(P->start_cell.floor, P->start_cell.row,P->start_cell.col));
                    P->throws_since_dir_change=(P->throws_since_dir_change+1)%4;
                } else {
                    printf("%c is at the starting area and rolls %d on the movement dice cannot enter the maze.\n",
                           P->name, move_die);
                    continue;
                }
            } else {
                bool will_roll_dir = (P->throws_since_dir_change==3);
                int dir_face = -1;
                if (P->disoriented_left > 0) {
                    Direction new_dir = (Direction) rand_int(0,3);
                    sprintf(message,"%c rolls and %d on the movement dice and is disoriented and move in the %s and moves %d cells and is placed at the ",
                           P->name, move_die, direction_name(new_dir), move_die);
                    P->direction = new_dir;
                    P->disoriented_left--;
                    if (P->disoriented_left == 0) {
                        printf("%c has recovered from disorientation.\n", P->name);
                    }
                    P->throws_since_dir_change = (P->throws_since_dir_change+1)%4;
                } else if (will_roll_dir) {
                    dir_face = rand_int(1,6);
                    int d = set_direction(dir_face);  // 2..5 -> N/E/S/W, 1/6 -> -1 (keep)
                    if (d != -1) {
                        P->direction = (Direction)d;
                        sprintf(message,"%c rolls and %d on the movement dice and %s on the direction dice, changes direction to %s and moves %d cells and is now at ",
                               P->name, move_die, direction_name((Direction)d), direction_name(P->direction), move_die);
                    } else {
                        sprintf(message,"%c rolls and %d on the movement dice and moves %s by %d cells and is now at ",
                               P->name, move_die, direction_name(P->direction), move_die);
                    }
                    P->throws_since_dir_change=0;
                } else {
                    sprintf(message,"%c rolls and %d on the movement dice and moves %s by %d cells and is now at ",
                           P->name, move_die, direction_name(P->direction), move_die);
                    P->throws_since_dir_change++;
                }
                
                int steps=move_die;
                if (P->triggered_left > 0) {
                    steps *= 2;
                    P->triggered_left--;
                    if (will_roll_dir){
                        sprintf(message,"%c is triggered and rolls and %d on the movement dice and %d on direction die and move in the %s and moves %d cells and is placed at the ",
                               P->name, move_die,dir_face, direction_name(P->direction), steps);
                    } else {
                        sprintf(message,"%c is triggered and rolls and %d on the movement dice and move in the %s and moves %d cells and is placed at the ",
                               P->name, move_die, direction_name(P->direction), steps);
                    }
                }

                bool bawana=false , capture = false;
                Position before = P->position;
                long long mp_before = P->mp;
                int cells_moved = 0;
                long long cost = 0;
                bool move = attempt_move(P, steps, &bawana, &capture, &cells_moved, &cost);
                if (capture) {
                    P->in_maze = false;
                    printf(" Player %c stuck on infinity loop and sent to starting area\n", P->name);
                    continue;
                }
                else if (!move) {
                    //printf("%s %d.\n",message, get_cell_number(P->position));
                    printf("%c rolls and %d on the movement dice and cannot move in the %s. Player remains at %d\n",
                           P->name, move_die, direction_name(P->direction), get_cell_number(P->position));
                    P->position = before;
                    P->mp = mp_before;
                    blocked_cost(P);
                } else if (!bawana) {
                    printf("%s %d.\n",message, get_cell_number(P->position));
                    printf("%c moved %d that cost %lld movement points and is left with %lld and is moving in the %s.\n",
                           P->name, cells_moved, cost, P->mp, direction_name(P->direction));
                    bool captured=false;
                    check_capture(P, &captured);
                }// else if (move) {
                //     printf("%c moved %d that cost %lld movement points and is left with %lld and is moving in the %s.\n",
                //            P->name, cells_moved, cost, P->mp, direction_name(P->direction));
                // }
                // win check
                if (P->in_maze && same_pos(P->position, FLAG)) {
                    printf("\n===============================================================\n");
                    printf(" Player %c captured the flag at [%d,%d,%d]!\n", P->name, P->position.floor, P->position.row, P->position.col);
                    printf(" GAME OVER in %d rounds!\n", game_round+1);
                    printf("===============================================================\n");
                    game_over=true;

                    print_debug_out();


                    return 0;
                }


            }

        }

    }
    if (!game_over) {
        printf("\nNo winner within %d rounds.\n", GAME_ROUNDS);
        printf(" Game ended.\n");
    }
    print_debug_out();


    return 0;
    
}





void print_debug_out() {
    if (DEBUG) {
        printf("\n\nDEBUG: total bawana visits: %d\n", debug_bawana_visits);
        printf("DEBUG: stairs: %d\n", debug_stairs_taken);
        printf("DEBUG: poles: %d\n", debug_poles_taken);
        printf("DEBUG: wall blocks: %d\n", debug_wall_blocks);
    }
}

void print_stair_dir() {
    for (int i=0; i <n_stairs;i++){
        printf("  Stair%d at [%d,%d,%d,%d,%d,%d] to %s\n",i+1,STAIRS[i].s_floor, STAIRS[i].s_row, STAIRS[i].s_col, STAIRS[i].e_floor, STAIRS[i].e_row, STAIRS[i].e_col, (STAIRS[i].mode==UP)? "up" : (STAIRS[i].mode==DOWN) ? "down":"Bi-Directional");
    }
    printf("\n");
}


/*
returns a randomized integer value between low and high (inclusive)
*/

int rand_int(int low, int high) {
    return low + (rand() % (high-low +1));
}


// returns true if direction valid

bool is_valid_direction(int d) {
    return d>=0 && d<=3;
}

// returns string of direction for printing

char* direction_name(Direction dir) {
    if (!is_valid_direction(dir)) return "Not Valid";
    switch (dir) {
        case NORTH: return "NORTH";
        case EAST: return "EAST";
        case WEST: return "WEST";
        case SOUTH: return "SOUTH";
    }
}

// set direction
int set_direction(int die) {
    switch(die) {
        case 2: return NORTH;
        case 3: return EAST;
        case 4: return SOUTH;
        case 5: return WEST;
        default: return -1;
    }
}

char *get_bawana_type_name(BawanaType type){
    switch (type)
            {
            case FOOD_P:
                return "bawana food poisoning";
            case POINTS:
                return "bawana normal cell";
            case DISOR:
                return "bawana disoriented cell";
            case TRIG:
                return "bawana triggered cell";
            case HAPPY:
                return "bawana happy cell";
            default:
                return "bawana cell";
            }
}


/*
returns cell pointer taking floor, row, column
*/
Cell* get_cell (int floor, int row, int col) {
    if (!is_in_board(row, col)) return NULL;
    return &BOARD[floor][row][col];
}

/*
returns true if the cell is within the board (not considering the restricted areas, only outer box)
*/
bool is_in_board (int row, int col) {
    return (row >= 0 && row < ROWS && col >= 0 && col < COLUMNS);
}

bool is_cell_available(int floor, int row, int col) {
    if (!is_in_board(row, col)) return false;
    Cell *cell = get_cell(floor, row, col);
    if (cell->kind==NORMAL) return true;
    return false;
}

bool is_cell_available_for_poles(int floor, int row, int col) {
    if (!is_in_board(row, col)) return false;
    Cell *cell = get_cell(floor, row, col);
    if (cell->kind==NORMAL || cell->kind==START) return true;
    return false;
}


void set_cell (Cell *cell, CellKind kind, int consumables, int add, int mul) {
    cell->kind = kind;
    cell->consumable = consumables;
    cell->bonus_add = add;
    cell->bonus_mul = mul;
    for (int i = 0; i< CELLS; i++){
        cell->stair[i] = NULL;
    }
    cell->num_stairs=0;
    cell->pole = NULL;
}

void init_cells(){
    for (int f=0; f<FLOORS; f++) {
        for (int r = 0; r < ROWS; r++){
            for (int c=0; c < COLUMNS; c++){
                Cell *cell = &BOARD[f][r][c];
                Coord coordinates;
                coordinates.col =c;
                coordinates.row = r;
                coordinates.floor = f;
                cell->coord = coordinates;
                if (f==0){
                    if(r>=START_AREA_ROW_START && r <= START_AREA_ROW_END && c >= START_AREA_COL_START && c<=START_AREA_COL_END){
                        set_cell(cell, START, -1, 0, 1);
                        continue;
                    }
                    if (r>=BAWANA_ROW_START && r <= BAWANA_ROW_END && c >= BAWANA_COL_START && c <= BAWANA_COL_END){
                        set_cell(cell, BAWANA, -1, 0, 1);
                        continue;
                    }
                    if (r == BAWANA_ROW_START-1 && c >= BAWANA_COL_START-1 && c <= BAWANA_COL_END) {
                        set_cell(cell, WALL, -1, 0, 1);
                        continue;
                    }
                    if (r >= BAWANA_ROW_START && r <= BAWANA_ROW_END &&  c == BAWANA_COL_START-1) {
                        set_cell(cell, WALL, -1, 0, 1);
                        continue;
                    }
                }
                if (f==1){
                    bool in_left   = (c >= F1_RECT1_COL_START && c <= F1_RECT1_COL_END);          // 0..7
                    bool in_right  = (c >= F1_RECT2_COL_START && c <= F1_RECT2_COL_END);    // 16..24
                    bool in_bridge = (r >= F1_BRIDGE_ROW_START && r <= F1_BRIDGE_ROW_END &&
                                    c >= F1_BRIDGE_COL_START && c <= F1_BRIDGE_COL_END);        // 6..9, 8..16
                    if (!(in_left || in_right || in_bridge)) {
                        set_cell(cell, NONE, -1, 0, 1);
                        continue;
                    }
                }

                if (f==2){
                    if(!(c >= F2_RECT_COL_START && c<=F2_RECT_COL_END)){
                        set_cell(cell,NONE,-1,0,1);
                        continue;
                    }
                }
                set_cell(cell,NORMAL, -1, 0, 1);
            }
        }
    }
}

int min(int a, int b) {
    if (a>b) return b;
    return a;
}

int max(int a, int b) {
    if (a>b) return a;
    return b;
}

void add_wall(int floor, int row1,  int col1, int row2, int col2) {
    if (!(is_in_board(row1, col1)) || !(is_in_board(row2,col2))) return;
    if (!((row1==row2) || (col1==col2))) return;
    if (row1==row2){
        for (int i = min(col1,col2); i <= max(col1, col2); i++){
            BOARD[floor][row1][i].kind = WALL;
        }
    }
    if (col1==col2){
        for (int i = min(row1,row2); i <= max(row1, row2); i++){
            BOARD[floor][i][col1].kind = WALL;
        }
    }
}

void init_walls(){
    for (int i=0; i < n_walls; i++){
        add_wall(WALLS[i].floor,WALLS[i].s_row,WALLS[i].s_col, WALLS[i].e_row, WALLS[i].e_col);
    }
}

void consumables_randomize(){
    Cell *cells[CELLS];
    int n_active = 0;
    for (int f=0; f < FLOORS; f++) {
        for (int r=0; r < ROWS; r++){
            for (int c=0; c < COLUMNS; c++) {
                if (!(is_cell_available(f,r,c))) continue;
                cells[n_active++] = &BOARD[f][r][c];
            }
        }
    }

    int con_none = (int) (0.25*n_active);
    int con_1 = (int) (0.35*n_active);
    int bonus_12 = (int) (0.25*n_active);
    int bonus_35 = (int) (0.10*n_active);
    int mul_23 = n_active - (con_none + con_1 + bonus_12 + bonus_35);


    int added_cells =0;
    #define EMPTY(random_cell) ((random_cell)->consumable == -1 && (random_cell)->bonus_add == 0 && (random_cell)->bonus_mul == 1)
    // consumables 0
    int i = 0;
    while (i < con_none && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->consumable =0;
        i++, added_cells++; 
    }
    // consumable 1-4
    i=0;

    while (i < con_1 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->consumable = rand_int(1,4);
        if (DEBUG) printf("DEBUG: INITIALIZE CONSUMABLES, Cell [%d,%d,%d] got consumable %d\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, random_cell->consumable);
        i++, added_cells++; 
    }
    
    // bonus 1,2
    i=0;
    while (i < bonus_12 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->bonus_add = rand_int(1,2);
        if (DEBUG) printf("DEBUG: INITIALIZE CONSUMABLES, Cell [%d,%d,%d] got bonus %d\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, random_cell->bonus_add);
        i++, added_cells++; 
    }

    // bonus 3..5
    i=0;
    while (i < bonus_35 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->bonus_add = rand_int(3,5);
        if (DEBUG) printf("DEBUG: INITIALIZE CONSUMABLES, Cell [%d,%d,%d] got bonus %d\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, random_cell->bonus_add);
        i++, added_cells++; 
    }
    // mul 2,3
    i=0;
    while (i < mul_23 && added_cells < n_active){
        Cell *random_cell = cells[rand_int(0, n_active-1)];
        if (!EMPTY(random_cell)) continue;
        random_cell->bonus_mul = rand_int(2,3);
        if (DEBUG) printf("DEBUG: INITIALIZE CONSUMABLES, Cell [%d,%d,%d] got bonus multiplier %d\n", random_cell->coord.floor, random_cell->coord.row, random_cell->coord.col, random_cell->bonus_mul);
        i++, added_cells++; 
    }

}

// bawana

void initialize_bawana() {
    BawanaType bw_cells[BAWANA_CELLS];
    int food_p = 3, trig = 3, disor=3, happy=3, points=4;
    int added_cells =0;
    for (int i=0; i < BAWANA_CELLS; i++) {
        bw_cells[i] = POINTS;
    }
    // food poisoning
    int i = 0;
    while (i < food_p && added_cells < BAWANA_CELLS){
        BawanaType *random_cell = &bw_cells[rand_int(0, BAWANA_CELLS-1)];
        if (*random_cell!=POINTS) continue;
        *random_cell=FOOD_P;
        i++, added_cells++;
    }
    // disoreadted
    i = 0;
    while (i < trig && added_cells < BAWANA_CELLS){
        BawanaType *random_cell = &bw_cells[rand_int(0, BAWANA_CELLS-1)];
        if (*random_cell!=POINTS) continue;
        *random_cell=TRIG;
        i++, added_cells++; 
    }
    // happy
    i = 0;
    while (i < happy && added_cells < BAWANA_CELLS){
        BawanaType *random_cell = &bw_cells[rand_int(0, BAWANA_CELLS-1)];
        if (*random_cell!=POINTS) continue;
        *random_cell=HAPPY;
        i++, added_cells++; 
    }

    added_cells=0;
    for (int row=0; row<=BAWANA_ROW_END-BAWANA_ROW_START; row++) {
        for (int col=0; col<=BAWANA_COL_END-BAWANA_COL_START; col++){
            BAWANA_MAP[row][col] = bw_cells[added_cells++];
        }
    }

}

BawanaType random_bawana_cell () {
    int ran_col = rand_int(0, BAWANA_COL_END-BAWANA_COL_START);
    int ran_row = rand_int(0, BAWANA_ROW_END-BAWANA_ROW_START);
    return BAWANA_MAP[ran_row][ran_col];
}

void exit_bawana(Player *p) {
    p->position= (Position) {0, BAWANA_EXIT_ROW, BAWANA_EXIT_COL};
    p->direction=NORTH;
}


void apply_bawana(Player *p) {
    debug_bawana_visits++;
    BawanaType type = random_bawana_cell();
    printf("%c is place on a %s and effects take place.\n", p->name, get_bawana_type_name(type));
    if (type==FOOD_P) {
        p->turns_skipped=3;
        printf("%c eats from Bawana and have a bad case of food poisoning. Will need three rounds to recover.\n", p->name);
    } else if (type==DISOR) {
        p->mp += 50;
        p->disoriented_left = DISORIENTED_THROWS;
        exit_bawana(p);
        printf("%c eats from Bawana and is disoriented and is placed at the entrance of Bawana with 50 movement points.\n", p->name);
    } else if (type==TRIG) {
        p->mp += 50;
        p->triggered_left = TRIGGERED_THROWS;
        exit_bawana(p);
        printf("%c eats from Bawana and is triggered due to bad quality of food. %c is placed at the entrance of Bawana with 50 movement points.\n", p->name, p->name);
    } else if (type==HAPPY) {
        p->mp += 200;
        exit_bawana(p);
        printf("%c eats from Bawana and is happy. %c is placed at the entrance of Bawana with 200 movement points.\n", p->name, p->name);
    } else {
        long long pts = rand_int(10,100);
        p->mp += pts;
        exit_bawana(p);
        printf("%c eats from Bawana and earns %lld movement points and is placed at the %d.\n", p->name, pts, get_cell_number(p->position));
    }

}

void randomize_stairs_direction() {
    for (int i=0; i <n_stairs; i++) {
        int random_012 = rand_int(0,2); // use bi direction too
        STAIRS[i].mode = random_012;
    }

}

void stairs_bidirectional() {
    for (int i=0; i < n_stairs; i++) {
        STAIRS[i].mode = BI;
    }
}

void initialize_stairs() {
    for (int i = 0; i < n_stairs; i++) {
        Stair *stair = &STAIRS[i];
        if (!(is_cell_available(stair->e_floor, stair->e_row, stair->e_col) && is_cell_available(stair->s_floor, stair->s_row, stair->s_col))) {
            if (DEBUG) printf("DEBUG: Stair [%d,%d,%d,%d,%d,%d] skipped\n", stair->s_floor,stair->s_row,stair->s_col,stair->e_floor,stair->e_row,stair->e_col);
            continue;
        }
        Cell *start_cell = get_cell(stair->s_floor, stair->s_row, stair->s_col);
        if (start_cell != NULL) {
            if (start_cell->num_stairs>=2) continue;
            start_cell->stair[start_cell->num_stairs++] = stair;
        }
        if (!(stair->s_floor == stair->e_floor && stair->s_row == stair->e_row && stair->s_col == stair->e_col)) {
            Cell *end_cell = get_cell(stair->e_floor, stair->e_row, stair->e_col);
            if (end_cell != NULL) {
                end_cell->stair[end_cell->num_stairs++] = stair;
            }
        }
        block_stair_passing_cells(stair);
        if (DEBUG) printf("DEBUG: Stair [%d,%d,%d,%d,%d,%d] added\n", stair->s_floor,stair->s_row,stair->s_col,stair->e_floor,stair->e_row,stair->e_col);
    }

    stairs_bidirectional();
    //stairs_up();
}


/*
adds poles to all cells including ones below the starting one
example if pole is starting at 3,0,3,10 it is be available to  2 nd floor too at 1,3,10 and in 3rd floor 2,3,10
*/
void initialize_poles() {
    for (int i = 0; i < n_poles; i++) {
        Pole *pole = &POLES[i];
        bool valid = true;
        for (int floor = pole->s_floor; floor <= pole->e_floor; floor++) {
            Cell *cell = get_cell(floor, pole->row, pole->col);
            if (!is_cell_available_for_poles(cell->coord.floor,cell->coord.row,cell->coord.col)){
                valid = false;
                if (DEBUG) printf("DEBUG: Pole [%d,%d,%d,%d] not valid skipped\n", pole->s_floor, pole->e_floor, pole->row, pole->col);
                break;
            } 
        }
        if (!valid) continue;
        for (int floor = pole->s_floor; floor <= pole->e_floor; floor++) {
            Cell *cell = get_cell(floor, pole->row, pole->col);
            if (cell != NULL) {
                if (cell->pole != NULL) {
                    if (pole->s_floor <= cell->pole->s_floor && pole->e_floor >= cell->pole->e_floor){
                        cell->pole = pole; 
                    }
                    
                } else {
                    cell->pole = pole; 
                }
            }
        }
        if (DEBUG) printf("DEBUG: Pole [%d,%d,%d,%d] added\n", pole->s_floor, pole->e_floor, pole->row, pole->col);

    }
}


int man_best_distance(Position pos1, Position pos2) {
    return (abs(pos1.floor-pos2.floor)+1) * (abs(pos1.row - pos2.row) + abs(pos1.col - pos2.col));
}

bool can_use_stair(Stair *stair, Position current_pos) {
    

    //  start position
    if (current_pos.floor == stair->s_floor &&
        current_pos.row == stair->s_row &&
        current_pos.col == stair->s_col) {
        return (stair->mode == BI || stair->mode == UP);
    }

    // end position
    if (current_pos.floor == stair->e_floor &&
        current_pos.row == stair->e_row &&
        current_pos.col == stair->e_col) {
        return (stair->mode == BI || stair->mode == DOWN);
    }

    return false;
}

Cell *check_stairs(Cell *current) {
    if (current->num_stairs == 0) return NULL;
    Position current_pos = {current->coord.floor, current->coord.row, current->coord.col};

    if (DEBUG) printf("DEBUG:[%d,%d,%d] has %d stairs\n",
                      current_pos.floor, current_pos.row, current_pos.col, current->num_stairs);

    Cell *best_destination = NULL;
    int best_distance = INT_MAX;


    for (int i = 0; i < current->num_stairs; i++) {
        Stair *stair = current->stair[i];

        if (DEBUG) printf("DEBUG:  Stair %d: [%d,%d,%d] to [%d,%d,%d], mode=%d\n", i,
                          stair->s_floor, stair->s_row, stair->s_col,
                          stair->e_floor, stair->e_row, stair->e_col,
                          stair->mode);

        if (!can_use_stair(stair, current_pos)) {
            if (DEBUG) printf("DEBUG:cant use %d stair\n", i);
            continue;
        }

        Position dest_pos;
        if (current_pos.floor == stair->s_floor &&
            current_pos.row == stair->s_row &&
            current_pos.col == stair->s_col) {
            // start to end
            dest_pos = (Position){stair->e_floor, stair->e_row, stair->e_col};
            if (DEBUG) printf("DEBUG: stair st to end [%d,%d,%d]\n",
                              dest_pos.floor, dest_pos.row, dest_pos.col);
        } else if (current_pos.floor == stair->e_floor &&
                   current_pos.row == stair->e_row &&
                   current_pos.col == stair->e_col) {
            //at end to start
            dest_pos = (Position){stair->s_floor, stair->s_row, stair->s_col};
            if (DEBUG) printf("DEBUG:stair end to start [%d,%d,%d]\n",
                              dest_pos.floor, dest_pos.row, dest_pos.col);
        } else {
            if (DEBUG) printf("DEBUG:not at either end");
        }

        // stairs lead to samepos
        if (dest_pos.floor == current_pos.floor &&
            dest_pos.row == current_pos.row &&
            dest_pos.col == current_pos.col) {
            continue; 
        }

        Cell *dest_cell = get_cell(dest_pos.floor, dest_pos.row, dest_pos.col);
        if (dest_cell == NULL || !is_cell_available(dest_pos.floor, dest_pos.row, dest_pos.col)) {
            continue;
        }

        int distance = man_best_distance(dest_pos, FLAG);


        if (best_destination == NULL || distance < best_distance) {
            best_destination = dest_cell;
            best_distance = distance;
            if (DEBUG) printf("DEBUG:best dest  [%d,%d,%d] (dist %d)\n",
                              dest_pos.floor, dest_pos.row, dest_pos.col, distance);
        }
    }

    if (DEBUG) {
        if (best_destination != NULL) {
            printf("DEBUG: stair final [%d,%d,%d] to [%d,%d,%d]\n",
                   current_pos.floor, current_pos.row, current_pos.col,
                   best_destination->coord.floor, best_destination->coord.row, best_destination->coord.col);
        } else {
            printf("DEBUG: no usable stair at [%d,%d,%d]\n",
                   current_pos.floor, current_pos.row, current_pos.col);
        }
    }

    return best_destination;
}


// poles
Cell *check_pole(Cell *current) {
    if (!current->pole) return NULL;
    if (current->coord.floor > current->pole->s_floor) {
        return get_cell(current->pole->s_floor, current->pole->row, current->pole->col);
    }
    return NULL;
}
// movement
void apply_cell_effect(Player *p, const Cell *cell){
    if (cell->consumable >= 0) {
        p->mp -= cell->consumable;
        if (DEBUG) printf("DEBUG: %i MP lost(total: %lli)\n", cell->consumable,p->mp);
    }
    else if (cell->bonus_add > 0) {
        p->mp += cell->bonus_add;
        if (DEBUG) printf("DEBUG: %i MP gain(total: %lli)\n", cell->bonus_add,p->mp);
        
    }
    else if (cell->bonus_mul > 1){
        p->mp *= cell->bonus_mul;
        if (DEBUG) printf("DEBUG: x%i MP multiply(total: %lli)\n", cell->bonus_mul,p->mp);
        
    } 
} 



void check_capture(Player* P, bool *captured) {
    *captured = false;
    for (int i = 0; i < NUM_PLAYERS; i++) {
        Player *Q = &PLAYERS[i];
        if (Q == P) continue;
        if (!Q->in_maze) continue;
        if (P->position.floor==Q->position.floor && P->position.row==Q->position.row && P->position.col == Q->position.col) {
            *captured = true;
            printf("Player %c captures player %c and sent to start position\n", P->name, Q->name);
            Q->in_maze=false;
            Q->direction=Q->start_direction;
            Q->position=Q->init_pos;
        }
    }
}




bool attempt_move(Player *P, int steps, bool *sent_to_bawana, bool *capture, int *cells_moved, long long *cost) {
    *sent_to_bawana = false;
    *capture = false;
    *cells_moved = 0;
    *cost = 0;
    Position start = P->position;
    long long mp_start = P->mp;

    Position current = start;
    int remaining = steps;

    while (remaining > 0) {
        // 2nd check even in current
        if (!is_cell_available(current.floor,current.row,current.col) || !is_valid_direction(P->direction)){
             if (DEBUG) printf("DEBUG: Movement blocked at position [%d,%d,%d]\n", current.floor, current.row, current.col);
            P->position=start;
            P->mp = mp_start;
            return false;
        }
        Position next = (Position) {
            current.floor,
            current.row + dirRows[P->direction],
            current.col + dirCol[P->direction]
        };
        // next cell check
        if (!is_cell_available(next.floor,next.row, next.col) || !is_valid_direction(P->direction)) {
            CellKind blocked_cell_kind;
            char *block_type= "due to cell not in maze";
            if (is_in_board(next.row, next.col)) {
                blocked_cell_kind = get_cell(next.floor, next.row, next.col)->kind;
                switch (blocked_cell_kind)
                {
                    case WALL:
                        debug_wall_blocks++;
                        block_type = "by a Wall";
                        break;
                    case START:
                        block_type = "by the starting area";
                        break;      
                    case BAWANA:
                        block_type = "by the bawana area";
                        break;
                    default:
                        break;
                }
            }
            if (DEBUG) printf("DEBUG: Move blocked in [%d,%d,%d] %s\n", next.floor, next.row, next.col, block_type);
            P->position = start;
            P->mp = mp_start;
            *cells_moved=0;
            *cost=-2;
            return false;
        }

        current = next;
        Cell *cell_c = get_cell(current.floor, current.row, current.col);
        Cell *jumped = check_pole(cell_c); // prioritize pole
        if (jumped) {
            debug_poles_taken++;
            printf("%c lands on %d which is a pole cell. %c slides down and now placed at %d in floor %d\n",
                   P->name, get_cell_number((Position){current.floor, current.row, current.col}),
                   P->name, get_cell_number((Position){jumped->coord.floor, jumped->coord.row, jumped->coord.col}),
                   jumped->coord.floor);
            if (jumped->kind==START){
                P->in_maze=false;
                P->position=P->init_pos;
                printf("%c sent to starting position because %c took a pole to starting area\n", P->name, P->name);
                return true;
            }

            current = (Position) {jumped->coord.floor, jumped->coord.row, jumped->coord.col};
        } else {
            jumped = check_stairs(cell_c);
            if (jumped) {
                debug_stairs_taken++;
                printf("%c lands on %d which is a stair cell. %c takes the stairs and now placed at %d in floor %d\n",
                       P->name, get_cell_number((Position){current.floor, current.row, current.col}),
                       P->name, get_cell_number((Position){jumped->coord.floor, jumped->coord.row, jumped->coord.col}),
                       jumped->coord.floor);

                current = (Position) {jumped->coord.floor, jumped->coord.row, jumped->coord.col};
            }
        }

        long long mp_before_cell = P->mp;
        apply_cell_effect(P, get_cell(current.floor, current.row, current.col));
        long long mp_after_cell = P->mp;

        // MP COST (- cost + gain)
        if (mp_after_cell < mp_before_cell) {
            *cost += (mp_before_cell - mp_after_cell);
        }

        (*cells_moved)++;

        if (P->mp <= 0) {
            printf("%c movement points are depleted and requires replenishment. Transporting to Bawana.\n", P->name);
            P->position = current;
            apply_bawana(P);
            *sent_to_bawana=true;
            return true;
        }
        remaining--;

    }
    if (same_pos(current, start)) {
        *capture = true;
        P->position=P->init_pos;
        P->direction=P->start_direction;
        printf("Player %c stuck on a loop and sent to start position\n", P->name);
        return false;
    }
    P->position = current;
    return true;
}

void blocked_cost(Player *P) {
    P->mp -= 2;
    printf("%c moved 0 that cost 2 movement points and is left with %lld and is moving in the %s.\n",
           P->name, P->mp, direction_name(P->direction));
    if (P->mp <= 0) {
        printf("%c movement points are depleted and requires replenishment. Transporting to Bawana.\n", P->name);
        apply_bawana(P);
    }
}


// init

void place_flag_randomly() {
    while (true) {
        int floor = rand_int(0,2), row = rand_int(0,9), col = rand_int(0,24);
        if (!is_cell_available(floor,row,col)) continue;
        Cell *cell = get_cell(floor,row, col);
        if (cell->pole || cell->num_stairs > 0) continue;
        FLAG = (Position){floor,row,col};
        break; 
    }
}

void initialize_player(Player *player, char name, int start_row, int start_col, int first_row, int first_col, Direction dir){
    player->name = name;
    player->position = (Position) {0,start_row,start_col};
    player->init_pos = (Position) {0,start_row,start_col};
    player->start_cell = (Position) {0, first_row, first_col};
    player->direction = dir;
    player->start_direction = dir;
    player->in_maze = false;
    player->mp = 100;
    player->throws_since_dir_change=0;
    player->turns_skipped=0;
    player->disoriented_left=0;
    player->triggered_left=0;
}

void setup_players() {
    
    initialize_player(&PLAYERS[0],'A',6,12,5,12,NORTH);
    initialize_player(&PLAYERS[1],'B',9,8,9,7,WEST);
    initialize_player(&PLAYERS[2],'C',9,16,9,17,EAST);
    
}

bool same_pos(Position a, Position b) {
    return (a.floor==b.floor && a.row==b.row && a.col==b.col);
}

// load stairs from file
void load_stairs() {
    FILE *file = fopen("stairs.txt", "r");
    if (!file) { printf("Failed to open stairs.txt\n"); exit(1); }

    n_stairs = 0;
    char line[256];
    while (fgets(line, sizeof line, file) && n_stairs < CELLS) {
        int s_floor, s_row, s_col, e_floor, e_row, e_col;
        if (sscanf(line, " [ %d , %d , %d , %d , %d , %d ] ",
                   &s_floor, &s_row, &s_col, &e_floor, &e_row, &e_col) == 6) {
            STAIRS[n_stairs].s_floor = s_floor;
            STAIRS[n_stairs].s_row   = s_row;
            STAIRS[n_stairs].s_col   = s_col;
            STAIRS[n_stairs].e_floor = e_floor;
            STAIRS[n_stairs].e_row   = e_row;
            STAIRS[n_stairs].e_col   = e_col;
            STAIRS[n_stairs].mode = BI;
            n_stairs++;
        }
    }
    fclose(file);
    printf("Loaded %d stairs\n", n_stairs);
}

void load_poles() {
    FILE *file = fopen("poles.txt", "r");
    if (!file) { printf("failed to open poles.txt\n"); exit(1); }

    n_poles = 0;
    char line[256];
    while (fgets(line, sizeof line, file) && n_poles < CELLS) {
        int s_floor, s_row, s_col, e_floor;
        if (sscanf(line, " [ %d , %d , %d , %d ] ",
                   &s_floor, &e_floor, &s_row, &s_col) == 4) {
            POLES[n_poles].s_floor = s_floor;
            POLES[n_poles].row     = s_row;
            POLES[n_poles].col     = s_col;
            POLES[n_poles].e_floor = e_floor;
            n_poles++;
        }
    }
    fclose(file);
    printf("Loaded %d poles\n", n_poles);
}

void load_walls() {
    FILE *file = fopen("walls.txt", "r");
    if (!file) { printf("Error: Could not open walls.txt\n"); exit(1); }

    n_walls = 0;
    char line[256];
    while (fgets(line, sizeof line, file) && n_walls < CELLS) {
        int floor, s_row, s_col, e_row, e_col;
        if (sscanf(line, " [ %d , %d , %d , %d , %d ] ",
                   &floor, &s_row, &s_col, &e_row, &e_col) == 5) {
            WALLS[n_walls].floor = floor;
            WALLS[n_walls].s_row = s_row;
            WALLS[n_walls].s_col = s_col;
            WALLS[n_walls].e_row = e_row;
            WALLS[n_walls].e_col = e_col;
            n_walls++;
        }
    }
    fclose(file);
    printf("Loaded %d walls\n", n_walls);
}


void load_flag() {
    FILE *file = fopen("flag.txt", "r");
    if (!file) {
        printf("Cant open flag.txt\n");
        place_flag_randomly();
        printf("Flag Placed Randomly\n");
        return;
    }

    char line[256];
    if (fgets(line, sizeof line, file)) {
        int floor, row, col;
        if (sscanf(line, " [ %d , %d , %d ] ", &floor, &row, &col) == 3) {
            if (is_cell_available(floor, row, col)) {
                FLAG.floor = floor;
                FLAG.row   = row;
                FLAG.col   = col;
                printf("flag loaded from flag.txt: [%d,%d,%d]\n", floor, row, col);
                fclose(file);
                return;
            }
        }
    }
    fclose(file);
    place_flag_randomly();
    printf("can't get flag position, Flag Placed Randomly\n");
}


int get_cell_number(Position pos) {
    return pos.floor * 100 + pos.row * 10 + pos.col;
}


// since only 3 floors
void block_stair_passing_cells(Stair *stair) {
    int dif_floors = abs(stair->e_floor-stair->s_floor);
    if (dif_floors <= 1) return;
    int diff_row = abs(stair->e_row-stair->s_row);
    int diff_col = abs(stair->e_col-stair->s_col);
    int row = min(stair->e_row,stair->s_row)+(diff_row)/2;
    int col = min(stair->e_col,stair->s_col)+(diff_col)/2;
    add_wall(1,row,col,row,col);
}


void load_seed(){
    FILE *file = fopen("seed.txt","r");
    unsigned int seed;
    if (!file){
        printf("Can't open seed.txt, using random seed\n");
        srand((unsigned)time(NULL));
        return;
    }
    if(fscanf(file, "%u", &seed) == 1) {
        srand(seed);
        printf("seed %u\n", seed);
        fclose(file);
        return;
    }
    fclose(file);
    srand((unsigned)time(NULL));
    printf("Using random seed\n");
}
