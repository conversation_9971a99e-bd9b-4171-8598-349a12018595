Can't open seed.txt, using random seed
Loaded 6 stairs
Loaded 3 poles
Loaded 1 walls
DEBUG: INITIALIZE CONSUMABLES, Cell [1,2,6] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,2,4] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,8,18] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [2,7,14] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,2,1] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,10] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,20] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,0,24] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,5,1] got consumable 4
DEBUG: INITIALIZ<PERSON> CONSUMABLES, Cell [1,9,24] got consumable 1
DEBUG: INIT<PERSON><PERSON>I<PERSON><PERSON> CONSUMABLES, Cell [1,7,9] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [2,7,12] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,5,3] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,2,14] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,1,20] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,1] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,4,17] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,4,23] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,4,11] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [2,1,16] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [2,1,10] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,19] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,16] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,22] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,3] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,22] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,1] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,20] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,8,9] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,3,9] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,3,5] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,5,0] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [2,4,16] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,14] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [2,2,11] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [2,0,8] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,9,11] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,9] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,8] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,15] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,10] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [2,3,12] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,4,16] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,19] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,2] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,2,19] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,4,8] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,9,9] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,4,22] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,13] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [2,4,11] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,1,7] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,5,12] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,21] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,5,14] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,17] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,4,18] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,20] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,8,14] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [2,6,15] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [2,4,9] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,16] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,3] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,11] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [2,6,11] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,7,19] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,3,11] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,11] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,4,14] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,24] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,18] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,0] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,0,0] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,5] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,2,23] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,5,4] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,8,3] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,5] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,24] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [2,5,14] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,4,10] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,19] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,16] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,1] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,21] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,0,16] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,6] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [2,1,11] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,18] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,2,22] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,1,1] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,1,22] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,1] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,14] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,23] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,0,18] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,8] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,2,23] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,21] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,7] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,3,8] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [2,7,10] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,8,1] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,9,3] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,6] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,5,8] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,3,6] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,4,4] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,8,5] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,8,19] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,5,24] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,8,6] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,3,19] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,11] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,6,19] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,4] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,4,18] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,13] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,2,15] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,18] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [2,0,13] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,12] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,2,21] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [2,4,14] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,5] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,22] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,4,10] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,6,3] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,12] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,2,11] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,2,3] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,2,14] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,7,15] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,5,12] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,14] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [2,9,12] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,12] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,7,2] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,24] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [2,5,9] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,9,6] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,17] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,2,17] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [2,7,9] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,5,11] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [2,0,16] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,14] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,8] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,3,1] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,1,0] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,5,18] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,4] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,23] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,5,0] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,0,23] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,8,0] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,17] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,11] got consumable 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,5,22] got consumable 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,7] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,0,14] got consumable 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,2,0] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,6] got consumable 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,3] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,8,16] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,2,16] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,11] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,1,24] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,0] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,2,24] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,10] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,1,3] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,9,8] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,6,5] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,9,10] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,8,11] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,4,5] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,0,6] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,3,10] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,4,0] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,4,15] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,8,12] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,4,15] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,8,7] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,6,13] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,9,15] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,1,12] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,5,7] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,5,13] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,6,9] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,1] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,1,23] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,10] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,9,16] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,2,10] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,3,3] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,8,4] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,3,20] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,0,22] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,10] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,5,17] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,3,21] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,9] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,15] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,4] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,6] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,7,8] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,11] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,7,16] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,1,4] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,24] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,1,16] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,8,10] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,23] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,6,14] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,4,4] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,14] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,22] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,0] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,6] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,5,16] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,1,8] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,0,12] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,1,6] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,9,0] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,21] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,15] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,4,20] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,9,4] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,3,15] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,8] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,7,6] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,0] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,5,19] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,1,17] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,8] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,15] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,0,15] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,19] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,3,17] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,12] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,4,1] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,9,14] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,4,19] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,3,16] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,17] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,6,18] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,3,24] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,9] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,4] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,5,23] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,2,18] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,9,17] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,0,21] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,9,2] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,7,3] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,7] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [2,7,13] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,4,13] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,19] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,7,1] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,6] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,12] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,9] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,4] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,7,0] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,5,20] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,4,6] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,5,4] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,6,7] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,4,0] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,0] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [0,2,2] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,10] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,4,12] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,9,7] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,5,16] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,5,21] got bonus 1
DEBUG: INITIALIZE CONSUMABLES, Cell [1,2,4] got bonus 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,12] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [0,2,13] got bonus 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,19] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,1,5] got bonus 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,14] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,12] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,4] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,3] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,3] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,0,19] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [1,0,1] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,20] got bonus 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,1,18] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,20] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [2,6,16] got bonus 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,4,24] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,16] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [2,4,8] got bonus 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,0,5] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [0,2,9] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [0,9,5] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,7] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,23] got bonus 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,7,5] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,3,0] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,18] got bonus 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,4,9] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,21] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [1,2,22] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [0,5,16] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,0,20] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,24] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,5,2] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,0,7] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [2,0,11] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [1,8,13] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,3,4] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,4,16] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [1,3,16] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,7] got bonus 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,5,5] got bonus 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,4,17] got bonus 4
DEBUG: INITIALIZE CONSUMABLES, Cell [0,6,17] got bonus 3
DEBUG: INITIALIZE CONSUMABLES, Cell [2,0,10] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,1] got bonus 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,9,1] got bonus 5
DEBUG: INITIALIZE CONSUMABLES, Cell [2,5,10] got bonus multiplier 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,16] got bonus multiplier 3
DEBUG: INITIALIZE CONSUMABLES, Cell [1,7,10] got bonus multiplier 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,3,14] got bonus multiplier 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,6,6] got bonus multiplier 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,9] got bonus multiplier 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,0,17] got bonus multiplier 3
DEBUG: INITIALIZE CONSUMABLES, Cell [2,8,8] got bonus multiplier 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,22] got bonus multiplier 2
DEBUG: INITIALIZE CONSUMABLES, Cell [0,6,0] got bonus multiplier 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,6,2] got bonus multiplier 2
DEBUG: INITIALIZE CONSUMABLES, Cell [2,2,13] got bonus multiplier 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,6,0] got bonus multiplier 3
DEBUG: INITIALIZE CONSUMABLES, Cell [0,1,13] got bonus multiplier 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,0,3] got bonus multiplier 3
DEBUG: INITIALIZE CONSUMABLES, Cell [2,8,15] got bonus multiplier 2
DEBUG: INITIALIZE CONSUMABLES, Cell [1,9,13] got bonus multiplier 3
DEBUG: INITIALIZE CONSUMABLES, Cell